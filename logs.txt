[+12848 ms] I/flutter ( 3476): === LOGOUT REQUEST ===
[   +2 ms] I/flutter ( 3476): URL: https://api.sepesha.com/api/logout
[   +2 ms] I/flutter ( 3476): Headers: Bearer
eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL2tpdW5nby5zZXBlc2hhLmNvbSIsInN1YiI6MTU4LCJpYXQiOjE3NTI1ODM5MTYsImV4cCI6MTc1Mjc1NjcxNiwidXNlcl90eXBlIjoiY3VzdG9tZXIiLCJhdXRoX2tleSI6IjhjNDdjYzZjL
TZiOTQtNDZjNC1hZTU2LWFhNDUyMjg0YjI3MCIsImRhdGEiOnsiZmlyc3RfbmFtZSI6IkFBQSIsIm1pZGRsZV9uYW1lIjoiQkJCIiwibGFzdF9uYW1lIjoiQ0NDIiwicGhvbmVfbnVtYmVyIjoiNzE0NjA5MTM1IiwicGhvbmVjb2RlIjoiMjU1IiwiZW1haWwiOiJhYmN
AYWJjLmNvbSIsInByaXZhY3lfY2hlY2tlZCI6MSwidWlkIjoiOGM0N2NjNmMtNmI5NC00NmM0LWFlNTYtYWE0NTIyODRiMjcwIn19.LcqdUJY5k6OZ8HPSVYGTgGoKrU8EZveE9g7yvWSIMgg
[   +3 ms] I/flutter ( 3476): Body: {"refresh_token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NTI1ODM5MTYsImV4cCI6MTc1NTE3NTkxNn0.8_LNqF6ZeOJbLeJviP9Jr63k_PlyF42sunaLPgds03U"}
[   +1 ms] I/flutter ( 3476): ==================
[ +967 ms] I/flutter ( 3476): === LOGOUT RESPONSE ===
[   +2 ms] I/flutter ( 3476): Status Code: 442
[   +2 ms] I/flutter ( 3476): Response Headers: {x-powered-by: PHP/8.3.23, alt-svc: h3=":443"; ma=2592000, cache-control: no-cache, private, access-control-allow-origin: *, date: Tue, 15 Jul 2025       
13:36:49 GMT, status: 442 unknown status, content-length: 88, content-type: application/json, via: 0.0 Caddy}
[   +2 ms] I/flutter ( 3476): Response Body: {"status":false,"message":"The refresh token field is required.","code":442,"data":null}
[   +2 ms] I/flutter ( 3476): ===================

### Endpoint: POST /api/calculate-fare

**Request:**

```json
{
  "pickup_latitude": -6.7252535,
  "pickup_longitude": 39.2146804,
  "delivery_latitude": -6.778577,
  "delivery_longitude": 39.2514687
}
```

**Response:**

```json
{
  "status": true,
  "message": "Fare calculated successfully",
  "code": 200,
  "data": {
    "distance_km": 7.19,
    "pickup_coordinates": {
      "latitude": -6.7252535,
      "longitude": 39.2146804
    },
    "delivery_coordinates": {
      "latitude": -6.778577,
      "longitude": 39.2514687
    },
    "vehicle_types": [
      {
        "id": "2f5d5c90-89c1-48d6-a2de-8ecf3b4af2ad",
        "name": "Bodaboda",
        "base_price": 3000,
        "price_per_km": 400,
        "multiplier": 1,
        "total_fare": 5874.97,
        "estimated_time": "14 minutes"
      }
    ]
  }
}
```

---

### Endpoint: POST /api/find-drivers

**Request:**

```json
{
  "pickup_latitude": -6.7252535,
  "pickup_longitude": 39.2146804,
  "vehicle_type": "4 wheeler",
  "radius_km": 5
}
```

**Response:**

```json
{
  "status": false,
  "message": "The fee category id field is required.",
  "code": 442,
  "data": null
}
```

---

### Endpoint: POST /api/create-ride-booking

**Request:**

```json
{
  "pickup_latitude": -6.7252535,
  "pickup_longitude": 39.2146804,
  "delivery_latitude": -6.778577,
  "delivery_longitude": 39.2514687,
  "pickup_location": "76F7+VVX, Dar es Salaam, Tanzania",
  "delivery_location": "Victoria Noble Center",
  "distance_km": 7.19,
  "estimated_fare": 0.0,
  "payment_method": "cash"
}
```

**Response:**

```json
{
  "status": false,
  "message": "The customer id field is required.",
  "code": 442,
  "data": null
}
```
[+3998 ms] I/flutter ( 8101): === GET USER PROFILE ===
[   +2 ms] I/flutter ( 8101): Using UserData from Preferences
[   +4 ms] I/flutter ( 8101): Found user data: ZZZ RRR
[   +2 ms] I/flutter ( 8101): User Profile Data: {user: Instance of 'UserData', profile_photo_url: null, wallet_balance_tzs: null, wallet_balance_usd: null, preferred_payment_method: null,
is_verified: null, total_rides: null, average_rating: null}
[   +2 ms] I/flutter ( 8101): ============================
[+19371 ms] D/InputMethodManager( 8101): showSoftInput() view=io.flutter.embedding.android.FlutterView{421591e VFE...... .F....ID 0,0-1080,2088 #1 aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT
[ +273 ms] D/InputConnectionAdaptor( 8101): The input method toggled cursor monitoring on
[ +181 ms] D/InsetsController( 8101): show(ime(), fromIme=true)
[+3953 ms] D/InputConnectionAdaptor( 8101): The input method toggled cursor monitoring off
[+1946 ms] I/flutter ( 8101): === UPDATE USER PROFILE ===
[   +2 ms] I/flutter ( 8101): === GET CURRENT USER ID ===
[   +1 ms] I/flutter ( 8101): User ID from auth key: 2e48eaa5-98c9-4704-b9e8-eeffb32376c2
[   +2 ms] I/flutter ( 8101): =======================
[   +1 ms] I/flutter ( 8101): Request URL: https://api.sepesha.com/api/user/update-profile/2e48eaa5-98c9-4704-b9e8-eeffb32376c2
[   +2 ms] I/flutter ( 8101): Request Fields: {name: ZZZL, sname: RRR, middle_name: VVV, email: <EMAIL>, preferred_payment_method: cash}
[   +1 ms] I/flutter ( 8101): Request Files: []
[+1236 ms] I/flutter ( 8101): Response Status Code: 442
[   +2 ms] I/flutter ( 8101): Response Body: {"status":false,"message":"The first name field is required.","code":442,"data":null}
[   +2 ms] I/flutter ( 8101): Error updating profile: 442
[   +1 ms] I/flutter ( 8101): Response body: {"status":false,"message":"The first name field is required.","code":442,"data":null}
[   +8 ms] E/flutter ( 8101): [ERROR:flutter/runtime/dart_vm_initializer.cc(40)] Unhandled Exception: Looking up a deactivated widget's ancestor is unsafe.
[   +1 ms] E/flutter ( 8101): At this point the state of the widget's element tree is no longer stable.
[   +1 ms] E/flutter ( 8101): To safely refer to a widget's ancestor in its dispose() method, save a reference to the ancestor by calling dependOnInheritedWidgetOfExactType() in the widget's       
didChangeDependencies() method.
[   +2 ms] E/flutter ( 8101): #0      Element._debugCheckStateIsActiveForAncestorLookup.<anonymous closure> (package:flutter/src/widgets/framework.dart:4945:9)
[   +1 ms] E/flutter ( 8101): #1      Element._debugCheckStateIsActiveForAncestorLookup (package:flutter/src/widgets/framework.dart:4959:6)
[   +1 ms] E/flutter ( 8101): #2      Element.findAncestorStateOfType (package:flutter/src/widgets/framework.dart:5030:12)
[   +1 ms] E/flutter ( 8101): #3      ScaffoldMessengerState._isRoot (package:flutter/src/material/scaffold.dart:252:52)
[   +1 ms] E/flutter ( 8101): #4      ScaffoldMessengerState._updateScaffolds (package:flutter/src/material/scaffold.dart:242:11)
[   +1 ms] E/flutter ( 8101): #5      ScaffoldMessengerState.showSnackBar (package:flutter/src/material/scaffold.dart:359:7)
[   +2 ms] E/flutter ( 8101): #6      _UserProfileScreenState._saveProfile (package:sepesha_app/screens/user_profile_screen.dart:525:37)
[   +2 ms] E/flutter ( 8101): <asynchronous suspension>
[   +1 ms] E/flutter ( 8101): 
[ +254 ms] ══╡ EXCEPTION CAUGHT BY ANIMATION LIBRARY ╞═════════════════════════════════════════════════════════
                    The following assertion was thrown while notifying status listeners for AnimationController:
                    Looking up a deactivated widget's ancestor is unsafe.
                    At this point the state of the widget's element tree is no longer stable.
                    To safely refer to a widget's ancestor in its dispose() method, save a reference to the ancestor by
                    calling dependOnInheritedWidgetOfExactType() in the widget's didChangeDependencies() method.

                    When the exception was thrown, this was the stack:
                    #0      Element._debugCheckStateIsActiveForAncestorLookup.<anonymous closure> (package:flutter/src/widgets/framework.dart:4945:9)
                    #1      Element._debugCheckStateIsActiveForAncestorLookup (package:flutter/src/widgets/framework.dart:4959:6)
                    #2      Element.findAncestorStateOfType (package:flutter/src/widgets/framework.dart:5030:12)
                    #3      ScaffoldMessengerState._isRoot (package:flutter/src/material/scaffold.dart:252:52)
                    #4      ScaffoldMessengerState._updateScaffolds (package:flutter/src/material/scaffold.dart:242:11)
                    #5      ScaffoldMessengerState._handleSnackBarStatusChanged (package:flutter/src/material/scaffold.dart:421:9)
                    #6      AnimationLocalStatusListenersMixin.notifyStatusListeners (package:flutter/src/animation/listener_helpers.dart:243:19)
                    #7      AnimationController._checkStatusChanged (package:flutter/src/animation/animation_controller.dart:945:7)
                    #8      AnimationController._tick (package:flutter/src/animation/animation_controller.dart:963:5)
                    #9      Ticker._tick (package:flutter/src/scheduler/ticker.dart:269:12)
                    #10     SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1438:15)
                    #11     SchedulerBinding.handleBeginFrame.<anonymous closure> (package:flutter/src/scheduler/binding.dart:1269:11)
                    #12     _LinkedHashMapMixin.forEach (dart:_compact_hash:764:13)
                    #13     SchedulerBinding.handleBeginFrame (package:flutter/src/scheduler/binding.dart:1267:17)
                    #14     SchedulerBinding._handleBeginFrame (package:flutter/src/scheduler/binding.dart:1183:5)
                    #15     _invoke1 (dart:ui/hooks.dart:347:13)
                    #16     PlatformDispatcher._beginFrame (dart:ui/platform_dispatcher.dart:426:5)
                    #17     _beginFrame (dart:ui/hooks.dart:292:31)

                    The AnimationController notifying status listeners was:
                      AnimationController#7830b(⏭ 1.000; paused; for SnackBar)
                    ════════════════════════════════════════════════════════════════════════════════════════════════════
[+4281 ms] Another exception was thrown: Looking up a deactivated widget's ancestor is unsafe.